import React, { useState, useEffect } from 'react';
import { User, Mail, Building, Save, X, AlertCircle } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card } from '../ui/Card';
import { useAuth } from '../../contexts/AuthContext';
import type { Profile } from '../../lib/supabase';

interface ProfileEditProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

interface FormData {
  name: string;
  company: string;
}

interface FormErrors {
  name?: string;
  company?: string;
}

export const ProfileEdit: React.FC<ProfileEditProps> = ({
  isOpen,
  onClose,
  onSave,
}) => {
  const { profile, updateProfile } = useAuth();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    company: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (profile) {
      const initialData = {
        name: profile.name || '',
        company: profile.company || '',
      };
      setFormData(initialData);
      setHasChanges(false);
    }
  }, [profile, isOpen]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // 验证姓名
    if (!formData.name.trim()) {
      newErrors.name = '姓名不能为空';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = '姓名至少需要2个字符';
    } else if (formData.name.trim().length > 50) {
      newErrors.name = '姓名不能超过50个字符';
    }

    // 验证公司名称（可选）
    if (formData.company && formData.company.length > 100) {
      newErrors.company = '公司名称不能超过100个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 检查是否有变更
    const originalData = {
      name: profile?.name || '',
      company: profile?.company || '',
    };
    const newData = { ...formData, [field]: value };
    setHasChanges(
      newData.name !== originalData.name || 
      newData.company !== originalData.company
    );

    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setSaving(true);
      
      await updateProfile({
        name: formData.name.trim(),
        company: formData.company.trim() || null,
      });

      onSave();
      onClose();
    } catch (error) {
      console.error('Error updating profile:', error);
      // 这里可以添加错误提示
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      if (window.confirm('您有未保存的更改，确定要取消吗？')) {
        onClose();
      }
    } else {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card glass className="w-full max-w-md max-h-[90vh] overflow-y-auto p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold">编辑个人信息</h2>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="space-y-4">
          {/* 姓名输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              <User className="h-4 w-4 inline mr-2" />
              姓名 *
            </label>
            <Input
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="请输入您的姓名"
              error={errors.name}
            />
            {errors.name && (
              <div className="flex items-center mt-1 text-red-400 text-sm">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.name}
              </div>
            )}
          </div>

          {/* 邮箱显示（只读） */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              <Mail className="h-4 w-4 inline mr-2" />
              邮箱地址
            </label>
            <div className="px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-gray-400">
              {profile?.email}
            </div>
            <p className="text-xs text-gray-500 mt-1">邮箱地址不可修改</p>
          </div>

          {/* 公司输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              <Building className="h-4 w-4 inline mr-2" />
              公司名称
            </label>
            <Input
              value={formData.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              placeholder="请输入您的公司名称（可选）"
              error={errors.company}
            />
            {errors.company && (
              <div className="flex items-center mt-1 text-red-400 text-sm">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.company}
              </div>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 mt-8">
          <Button
            variant="ghost"
            onClick={handleCancel}
            disabled={saving}
          >
            取消
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            disabled={saving || !hasChanges || Object.keys(errors).length > 0}
            glow
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                保存中...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                保存更改
              </>
            )}
          </Button>
        </div>

        {/* 提示信息 */}
        {hasChanges && (
          <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
            <div className="flex items-center text-yellow-400 text-sm">
              <AlertCircle className="h-4 w-4 mr-2" />
              您有未保存的更改
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};
