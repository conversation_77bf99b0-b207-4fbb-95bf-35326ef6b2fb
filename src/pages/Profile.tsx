import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { User, Mail, Building, Calendar, Edit3, Save, X, Camera, Shield } from 'lucide-react';
import { Sidebar } from '../components/layout/Sidebar';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { AvatarUpload } from '../components/ui/AvatarUpload';
import { ProfileEdit } from '../components/profile/ProfileEdit';
import { useAuth } from '../contexts/AuthContext';

export const Profile: React.FC = () => {
  const { user, profile } = useAuth();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const handleAvatarUpdate = async (avatarUrl: string) => {
    setRefreshing(true);
    // 延迟一下再刷新，让用户看到成功状态
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  const handleEditSave = () => {
    setRefreshing(true);
    // 编辑保存后刷新页面
    setTimeout(() => {
      window.location.reload();
    }, 500);
  };



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (!user || !profile) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  if (refreshing) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p>更新中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Sidebar />

      <div className="lg:ml-64 p-4 md:p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto"
        >
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              个人中心
            </h1>
            <p className="text-gray-400 mt-2">管理您的个人信息和账户设置</p>
          </div>

          <div className="grid lg:grid-cols-3 gap-6 lg:gap-8">
            {/* 左侧：头像和基本信息 */}
            <div className="lg:col-span-1 order-1 lg:order-1">
              <Card glass className="p-6 text-center">
                {/* 头像区域 */}
                <div className="mb-6">
                  <AvatarUpload
                    currentAvatarUrl={profile.avatar_url}
                    onAvatarUpdate={handleAvatarUpdate}
                    className="mx-auto"
                  />
                </div>

                {/* 基本信息 */}
                <h2 className="text-xl font-semibold mb-2">{profile.name}</h2>
                <p className="text-gray-400 mb-4">{profile.email}</p>
                
                {/* 账户状态 */}
                <div className="flex items-center justify-center space-x-2 mb-4">
                  <Shield className="h-4 w-4 text-green-400" />
                  <span className="text-sm text-green-400">
                    {profile.is_admin ? '管理员账户' : '普通用户'}
                  </span>
                </div>

                {/* 注册时间 */}
                <div className="text-sm text-gray-400">
                  <div className="flex items-center justify-center space-x-2">
                    <Calendar className="h-4 w-4" />
                    <span>注册于 {formatDate(profile.created_at)}</span>
                  </div>
                </div>
              </Card>
            </div>

            {/* 右侧：详细信息和编辑 */}
            <div className="lg:col-span-2 space-y-6 order-2 lg:order-2">
              {/* 个人信息卡片 */}
              <Card glass className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold">个人信息</h3>
                  <Button
                    variant="ghost"
                    onClick={() => setIsEditModalOpen(true)}
                    className="text-purple-400 hover:text-purple-300"
                  >
                    <Edit3 className="h-4 w-4 mr-2" />
                    编辑
                  </Button>
                </div>

                <div className="space-y-4">
                  {/* 姓名 */}
                  <div className="flex items-center space-x-4">
                    <User className="h-5 w-5 text-gray-400 flex-shrink-0" />
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        姓名
                      </label>
                      <p className="text-white">{profile.name}</p>
                    </div>
                  </div>

                  {/* 邮箱 */}
                  <div className="flex items-center space-x-4">
                    <Mail className="h-5 w-5 text-gray-400 flex-shrink-0" />
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        邮箱地址
                      </label>
                      <p className="text-gray-400">{profile.email}</p>
                      <p className="text-xs text-gray-500 mt-1">邮箱地址不可修改</p>
                    </div>
                  </div>

                  {/* 公司 */}
                  <div className="flex items-center space-x-4">
                    <Building className="h-5 w-5 text-gray-400 flex-shrink-0" />
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        公司名称
                      </label>
                      <p className="text-white">{profile.company || '未填写'}</p>
                    </div>
                  </div>
                </div>
              </Card>

              {/* 账户统计信息 */}
              <Card glass className="p-6">
                <h3 className="text-xl font-semibold mb-4">账户统计</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="bg-gray-800/50 rounded-lg p-4 text-center sm:text-left">
                    <div className="text-2xl font-bold text-purple-400">0</div>
                    <div className="text-sm text-gray-400">API 调用次数</div>
                  </div>
                  <div className="bg-gray-800/50 rounded-lg p-4 text-center sm:text-left">
                    <div className="text-2xl font-bold text-blue-400">0</div>
                    <div className="text-sm text-gray-400">语音模型数量</div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </motion.div>
      </div>

      {/* 编辑模态框 */}
      <ProfileEdit
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSave={handleEditSave}
      />
    </div>
  );
};
